import request from "@/utils/request";
//协议档案
export default {
  // 列表
  list(data) {
    return request({
      url: "/contractInfo/queryList",
      method: "post",
      data,
    });
  },
  //获取运管单号下拉
  remoteOmOptions(data) {
    return request({
      url: "/contractInfo/queryList",
      method: "post",
      data,
    });
  },
  //邦定运管单号
  bind(data) {
    return request({
      url: "/contractInfo/bindOmApplyNo",
      method: "get",
      params: data,
    });
  },
  //变更
  change(data) {
    return request({
      url: "/contractInfo/change",
      method: "post",
      data,
    });
  },
  // 备注
  remark(data) {
    return request({
      url: "/contractInfo/remark",
      method: "post",
      data,
    });
  },
  //导出
  export(data) {
    return request({
      url: "/contractInfo/export",
      method: "post",
      data,
    });
  },
  //运管审批进度
  queryProcess(data) {
    return request({
      url: "/ledger/order/getApproveRecord",
      method: "get",
      params: data,
    });
  },
  //业务类型列表
  queryBusinessType(data) {
    return request({
      url: "/contractInfo/businessType",
      method: "get",
      params: data,
    });
  },
  //审批状态列表
  queryApplyStatus(data) {
    return request({
      url: "/contractInfo/applyStatus",
      method: "get",
      params: data,
    });
  },
  //场站名称列表
  queryStationName(data) {
    return request({
      url: "/contractInfo/stationName",
      method: "post",
      data,
    });
  },
  //合同协议方列表
  queryContractParty(data) {
    return request({
      url: "/contractInfo/contractParty",
      method: "post",
      data,
    });
  },
  //合同详情-基本信息
  baseInfoDetail(data) {
    return request({
      url: "/contractInfo/basicDetail",
      method: "get",
      params: data,
    });
  },
  //合同详情-预警信息
  queryWarnList(data) {
    return request({
      url: "/contractInfo/alarmDetail",
      method: "post",
      data,
    });
  },
  //合同详情-切换预警状态
  changeMonitorStatus(data) {
    return request({
      url: "/contractInfo/onOrOff",
      method: "post",
      data,
    });
  },
  //合同详情-关联项目列表
  associateProject(data) {
    return request({
      url: "/contractInfo/associatedProjects",
      method: "get",
      params: data,
    });
  },
  //合同详情-关联合同列表
  associateContract(data) {
    return request({
      url: "/project/batch/costList",
      method: "post",
      data,
    });
  },
  //合同详情-操作日志
  queryLog(data) {
    return request({
      url: "/contractInfo/log",
      method: "get",
      params: data,
    });
  },

  /**
   * 保存合同协议
   * @param {Object} data - 合同协议数据
   * @param {string} data.contractName - 合同协议名称
   * @param {string} data.contractType - 合同协议类型
   * @param {string} data.businessType - 业务类型
   * @param {string} data.omApplyNo - 运管申请单号
   * @param {string} data.changeApplyNo - 运管变更申请单号
   * @param {string} data.contractCode - 合同编码
   * @param {string} data.effectiveTime - 生效时间
   * @param {string} data.expireTime - 失效时间
   * @param {Array} data.file - 附件列表
   * @param {string} data.partA - 甲方
   * @param {string} data.partB - 乙方
   * @param {string} data.contractId - 合同ID（编辑时传入）
   * @returns {Promise} 返回保存结果
   */
  saveContract(data) {
    return request({
      url: "/contractInfo/save",
      method: "post",
      data,
    });
  },

  /**
   * 查询合同协议详情
   * @param {Object} data - 查询参数
   * @param {string} data.contractId - 合同ID
   * @returns {Promise} 返回合同详情
   */
  getContractDetail(data) {
    return request({
      url: "/contractInfo/basicDetail",
      method: "get",
      params: data,
    });
  },

  /**
   * 查询运管申请单号选项
   * @param {Object} data - 查询参数
   * @param {string} data.name - 搜索关键字
   * @returns {Promise} 返回运管申请单号列表
   */
  queryOmApplyNoOptions(data) {
    return request({
      url: "/contractInfo/queryList",
      method: "post",
      data: data,
    });
  },

  /**
   * 查询合同编码选项
   * @param {Object} data - 查询参数
   * @param {string} data.name - 搜索关键字
   * @returns {Promise} 返回合同编码列表
   */
  queryContractCodeOptions(data) {
    return request({
      url: "/contractInfo/contractCodeOptions",
      method: "get",
      params: data,
    });
  },

  /**
   * 根据运管申请单号获取基本信息
   * @param {Object} data - 查询参数
   * @param {string} data.omApplyNo - 运管申请单号
   * @returns {Promise} 返回运管申请单号对应的基本信息
   */
  getOmApplyNoInfo(data) {
    return request({
      url: "/contractInfo/applyInfo",
      method: "get",
      params: data,
    });
  },

  /**
   * 查询关联项目信息
   * @param {Object} data - 查询参数
   * @param {string} data.omApplyNo - 运管申请单号
   * @param {string} data.contractTypeName - 合同类型名称
   * @returns {Promise} 返回关联项目信息
   */
  getAssociatedProjects(data) {
    return request({
      url: "/contractInfo/associatedProjects",
      method: "get",
      params: data,
    });
  },

  /**
   * 查询合同预警信息
   * @param {Object} data - 查询参数
   * @param {number} data.contractId - 合同ID
   * @returns {Promise} 返回预警信息列表
   */
  getAlarmDetail(data) {
    return request({
      url: "/contractInfo/alarmDetail",
      method: "post",
      data,
    });
  },

  /**
   * 启用/禁用合同监控预警状态
   * @param {Object} data - 请求参数
   * @param {number} data.contractId - 合同ID
   * @param {string} data.monitorStatus - 监控状态(0:启用 1:禁用)
   * @returns {Promise} 返回操作结果
   */
  toggleMonitorStatus(data) {
    return request({
      url: "/contractInfo/onOrOff",
      method: "post",
      data,
    });
  },
};
