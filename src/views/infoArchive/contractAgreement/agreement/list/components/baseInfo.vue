<!-- 基本信息 -->
<template>
  <div>
    <el-card>
      <CommonTitle class="mb10" title="基本信息" />
      <BaseDescriptions :list="infoList" :column="2">
        <template #links="{itemVal}">
          <FileIcons
            :list="itemVal"
            :fileOptions="{ url: 'storePath', name: 'docName' }"
          ></FileIcons>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="合同协议方" />
      <BaseDescriptions :list="partyList" :column="2"> </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="其他信息" />
      <BaseDescriptions :list="otherList" :column="2">
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>
      <div v-if="menuName === '采购框架协议申请'">
        <h4>付款条件</h4>
        <vxe-grid
          resizable
          align="center"
          :columns="columns1"
          :data="baseInfo.tableData"
        ></vxe-grid>
      </div>
      <div v-if="menuName === '固定资产采购合同申请'">
        <h4>付款条件</h4>
        <vxe-grid
          resizable
          align="center"
          :columns="columns2"
          :data="baseInfo.tableData"
        ></vxe-grid>
      </div>
    </el-card>

    <!-- 新的其他信息展示卡片 -->
    <el-card v-if="showNewOtherInfo">
      <CommonTitle class="mb10" title="维保通其他信息" />

      <!-- 基础字段展示 -->
      <BaseDescriptions
        :list="newOtherInfoList"
        :column="2"
        v-if="newOtherInfoList.length > 0"
      >
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>

      <!-- 表格字段展示 -->
      <div
        v-for="tableField in currentTypeConfig.tableFields"
        :key="tableField.field"
        class="table-display-section"
      >
        <vxe-grid
          resizable
          align="center"
          :columns="getDisplayTableColumns(tableField.tableType)"
          :data="baseInfo[tableField.field] || []"
          max-height="400px"
        ></vxe-grid>
      </div>
    </el-card>
    <el-card v-if="isChanged">
      <CommonTitle class="mb10" title="维保通变更信息" />
      <BaseDescriptions :list="changeList" :column="2">
        <template #links="{itemVal}">
          <FileIcons
            :list="itemVal"
            :fileOptions="{ url: 'storePath', name: 'docName' }"
          ></FileIcons>
        </template>
      </BaseDescriptions>
    </el-card>
  </div>
</template>

<script>
import FileIcons from "@/components/FileIcons/index.vue";
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/infoArchive/contractAgreement/agreement.js";
import { OtherInfoDict } from "./config";
import { getConfigByType, getTableColumns } from "./otherInfoConfig.js";
export default {
  components: {
    CommonTitle,
    BaseDescriptions,
    FileIcons,
  },
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      columns1: [
        { title: "付款条件", field: "fktj" },
        { title: "付款比例（%）", field: "fkbl" },
      ],
      columns2: [
        { title: "付款条件", field: "fktj" },
        { title: "付款比例（%）", field: "fkbl" },
        { title: "付款金额", field: "fktj" },
        { title: "预计付款时间", field: "fktj" },
      ],
      infoList: [],
      partyList: [],
      changeList: [],
      baseInfo: {},
      menuName: "",
      contractType: "", // 合同协议类型
      // 天干顺序映射
      tianganOrder: [
        "甲",
        "乙",
        "丙",
        "丁",
        "戊",
        "己",
        "庚",
        "辛",
        "壬",
        "癸",
      ],
      // 字典数据
      dictOptions: {
        area_config: [],
        billing_method: [],
        sys_yes_no: [],
        revenue_model: [],
        agreement_type: [],
      },
      isChanged: false,
    };
  },
  created() {
    // this.getDetail();
    this.loadDictData();
  },
  computed: {
    otherList() {
      return OtherInfoDict[this.menuName]?.map((x) => {
        const val = this.baseInfo?.[x.value];
        return { ...x, value: x.formatter?.(val) || val };
      });
    },

    // 当前合同类型配置
    currentTypeConfig() {
      return getConfigByType(this.contractType);
    },

    // 是否显示新的其他信息卡片
    showNewOtherInfo() {
      return this.contractType && this.currentTypeConfig.name !== "未知类型";
    },

    // 新的其他信息列表
    newOtherInfoList() {
      if (!this.currentTypeConfig.displayFields) return [];

      return this.currentTypeConfig.displayFields.map((field) => ({
        ...field,
        title: field.title,
        value: this.baseInfo[field.value] || "",
      }));
    },
  },
  methods: {
    /**
     * 解析contractPartyJson并按天干顺序排序
     * @param {string|Object} contractPartyJson - JSON字符串或对象
     * @returns {Array} 排序后的合同协议方列表
     */
    parseContractPartyJson(contractPartyJson) {
      if (!contractPartyJson) return [];

      try {
        const partyData =
          typeof contractPartyJson === "string"
            ? JSON.parse(contractPartyJson)
            : contractPartyJson;

        // 将对象转换为数组并按天干顺序排序
        const partyArray = Object.entries(partyData).map(([key, value]) => ({
          title: key.endsWith("方") ? key : `${key}方`,
          value: value,
          order: this.tianganOrder.indexOf(key.replace("方", "")),
          hidden: false,
        }));

        // 按天干顺序排序，未找到的排在最后
        return partyArray
          .sort((a, b) => {
            if (a.order === -1 && b.order === -1) return 0;
            if (a.order === -1) return 1;
            if (b.order === -1) return -1;
            return a.order - b.order;
          })
          .map(({ title, value, hidden }) => ({ title, value, hidden }));
      } catch (error) {
        console.error("解析contractPartyJson失败:", error);
        return [];
      }
    },

    /**
     * 从单独的partA、partB等字段构建合同协议方列表
     * @param {Object} data - 接口返回的数据
     * @returns {Array} 合同协议方列表
     */
    buildPartyListFromSeparateFields(data) {
      const parties = [];

      this.tianganOrder.forEach((tiangan) => {
        const fieldMap = {
          甲: "partA",
          乙: "partB",
          丙: "partC",
          丁: "partD",
          戊: "partE",
        };

        const fieldName = fieldMap[tiangan];
        if (fieldName && data[fieldName]) {
          parties.push({
            title: `${tiangan}方`,
            value: data[fieldName],
            hidden: false,
          });
        }
      });

      return parties;
    },

    getDetail() {
      api.baseInfoDetail({ contractId: this.contractId }).then((res) => {
        this.baseInfo = res.data;
        // 使用contractTypeName作为合同类型显示
        this.menuName = res.data.contractTypeName;
        this.contractType = res.data.contractTypeName; // 设置合同协议类型
        this.infoList = [
          {
            title: "运管申请单号",
            value: res.data?.omApplyNo,
            copy: true,
          },
          {
            title: "合同编码",
            value: res.data?.contractNo,
            copy: true,
          },
          {
            title: "变更合同&协议申请编号",
            value: res.data?.changeApplyNo,
            copy: true,
          },
          {
            title: "合同协议类型",
            value: res.data?.contractTypeName,
          },
          {
            title: "合同&协议名称",
            value: res.data?.contractName,
          },
          {
            title: "业务类型",
            value: res.data?.businessType,
          },
          {
            title: "合同&协议生效时间",
            value: res.data?.effectiveTime,
          },
          {
            title: "合同&协议失效时间",
            value: res.data?.expireTime,
          },
          {
            title: "备注",
            value: res.data?.remark,
          },
          {
            title: "附件",
            value: res.data?.docList || [],
            slotName: "links",
          },
          {
            title: "合同协议状态",
            value: res.data?.status,
          },
          {
            title: "运管审批状态",
            value: res.data?.applyStatus,
          },
          {
            title: "场站名称",
            value: res.data?.stationName,
          },
          {
            title: "场站编码",
            value: res.data?.stationCode,
          },
          {
            title: "提交人",
            value: res.data?.submitUser,
          },
          {
            title: "提交时间",
            value: res.data?.submitTime,
          },
        ];
        this.changeList = [
          {
            title: "变更原因",
            value: res.data?.changeReason,
          },
          {
            title: "变更日期",
            value: res.data?.changeDate,
          },
          {
            title: "变更附件",
            value: res.data?.changeDocList || [],
            slotName: "links",
          },
        ];
        this.isChanged = !!res.data?.changeDate;
        // 处理合同协议方数据
        // 优先使用contractPartyJson，如果没有则使用单独的partA、partB等字段
        if (res.data?.contractPartyJson) {
          // 使用contractPartyJson数据并按天干顺序排序
          this.partyList = this.parseContractPartyJson(
            res.data.contractPartyJson
          );
        } else {
          // 使用传统的partA、partB等字段，按天干顺序排序
          this.partyList = this.buildPartyListFromSeparateFields(res.data);
        }
      });
    },

    // 获取表格显示列配置（只读模式）
    getDisplayTableColumns(tableType) {
      const columns = getTableColumns(tableType);
      // 转换为只读显示列配置
      return columns.map((column) => ({
        title: column.title,
        field: column.field,
        width: column.width,
        // 移除编辑相关配置，只保留显示
        formatter: ({ cellValue }) => {
          // 根据字段类型格式化显示值
          if (column.element === "el-input-number") {
            return cellValue !== null && cellValue !== undefined
              ? cellValue
              : "";
          }

          // 处理字典值转换为字典标签
          if (
            column.element === "el-select" &&
            column.props &&
            column.props.optionValue === "dictValue"
          ) {
            return this.formatDictValue(column.field, cellValue);
          }

          return cellValue || "";
        },
      }));
    },

    // 加载字典数据
    loadDictData() {
      // 检查 getDicts 方法是否存在
      if (typeof this.getDicts !== "function") {
        console.warn("getDicts method not found");
        return;
      }

      // 加载各种字典数据
      const dictTypes = [
        "area_config",
        "billing_method",
        "sys_yes_no",
        "revenue_model",
        "agreement_type",
      ];

      dictTypes.forEach((dictType) => {
        this.getDicts(dictType)
          .then((response) => {
            this.$set(this.dictOptions, dictType, response.data || []);
          })
          .catch((error) => {
            console.error(`Failed to load ${dictType} options:`, error);
            this.$set(this.dictOptions, dictType, []);
          });
      });
    },

    // 格式化字典值为字典标签
    formatDictValue(fieldName, dictValue) {
      if (!dictValue) return "";

      // 字段名到字典类型的映射
      const fieldToDictMap = {
        region: "area_config",
        billingMethod: "billing_method",
        hasLateFee: "sys_yes_no",
        revenueModel: "revenue_model",
        agreementType: "agreement_type",
      };

      const dictType = fieldToDictMap[fieldName];
      if (!dictType || !this.dictOptions[dictType]) {
        return dictValue;
      }

      // 查找对应的字典标签
      const dictItem = this.dictOptions[dictType].find(
        (item) => item.dictValue === String(dictValue)
      );

      return dictItem ? dictItem.dictLabel : dictValue;
    },
  },
};
</script>

<style lang="less" scoped>
.table-display-section {
  margin-top: 20px;

  h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .table-description {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #909399;
  }
}
</style>
