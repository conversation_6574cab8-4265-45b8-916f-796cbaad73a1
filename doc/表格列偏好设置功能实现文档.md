# 表格列偏好设置功能实现文档

## 1. 功能概述

表格列偏好设置功能允许用户自定义表格列的显示、隐藏、排序和宽度等配置，并将这些配置保存到后端，实现个性化的表格展示效果。

## 2. 技术架构

### 2.1 核心组件

- **BuseCrud 组件**: 基于 vxe-table 的表格组件，提供列自定义功能
- **VXE Table**: 底层表格组件，支持列配置的获取和设置
- **后端 API**: 提供偏好设置的保存、查询和删除功能

### 2.2 数据流程

1. 用户操作表格列配置（显示/隐藏、排序、调整宽度）
2. 前端获取当前表格配置状态
3. 调用后端 API 保存/取消偏好设置
4. 页面初始化时从后端恢复用户的偏好配置

## 3. 实现步骤

### 3.1 数据属性定义

在 Vue 组件的 data 中添加以下属性：

```javascript
data() {
  return {
    // 偏好设置相关数据
    storeData: {},                    // 存储表格偏好配置数据
    btnLoading: false,                // 偏好设置按钮加载状态
    restorePromise: null,             // 恢复偏好设置的Promise缓存
    isCustomColumn: false,            // 是否已开启列偏好设置
    isPreferenceInitialized: false,   // 标记表格偏好设置是否已初始化
  }
}
```

### 3.2 表格配置

在 tableProps 中添加 customConfig 配置：

```javascript
computed: {
  tableProps() {
    return {
      border: true,
      align: "center",
      resizable: true,
      showOverflow: "tooltip",
      toolbarConfig: {
        custom: true,
        slots: {
          buttons: "toolbar_buttons",  // 工具栏按钮插槽
        },
      },
      // 列自定义配置
      customConfig: {
        storage: true,
        checkMethod: ({ column }) => {
          // 可以添加列检查逻辑，返回false的列不允许自定义
          return true;
        },
        // 恢复偏好设置的方法
        restoreStore: () => {
          const that = this;
          // 如果未发起过请求，则创建Promise（仅在页面初始化时执行）
          if (!that.restorePromise) {
            that.restorePromise = (async () => {
              try {
                const res = await comApi.queryCustomColumn({
                  menuFlag: "uniqueMenuFlag", // 每个页面使用唯一的menuFlag
                });
                that.isCustomColumn = res.data.preferenceFlag === "Y";
                that.isPreferenceInitialized = true;
                // 根据服务端数据或默认值返回
                const preferenceData = that.isCustomColumn
                  ? JSON.parse(res.data.preferenceJson)
                  : {};
                // 保存偏好设置数据
                that.storeData = preferenceData;
                return preferenceData;
              } catch (e) {
                that.isPreferenceInitialized = true;
                that.storeData = {};
                return {};
              }
            })();
          }
          // 返回缓存的Promise
          return that.restorePromise;
        },
        // 更新偏好设置的方法
        updateStore({ id, type, storeData }) {
          return new Promise(async (resolve) => {
            that.storeData = storeData;
            if (that.isCustomColumn) {
              const res = await comApi.saveCustomColumn({
                preferenceJson: JSON.stringify(storeData),
                menuFlag: "uniqueMenuFlag", // 每个页面使用唯一的menuFlag
              });
              if (res.code === "10000") {
                that.$message.success("保存成功");
              }
            }
            resolve();
          });
        },
      },
      columnConfig: {
        maxFixedSize: 99,  // 最大固定列数
      },
      id: "uniqueTableId",  // 每个表格使用唯一的ID
    };
  }
}
```

### 3.3 工具栏按钮

在模板中添加工具栏按钮：

```vue
<template #toolbar_buttons>
  <div style="display: flex;justify-content: flex-end;width: 100%;">
    <el-button
      type="text"
      @click="handleCustom"
      style="margin-right: 10px;"
      :loading="btnLoading"
    >
      {{ isCustomColumn ? "已开启偏好设置" : "将自定义列设为偏好" }}
    </el-button>
  </div>
</template>
```

### 3.4 核心方法实现

```javascript
methods: {
  // 处理表格列偏好设置
  async handleCustom() {
    // 获取当前表格的列配置状态
    const currentStoreData = this.$refs.crud
      .getVxeTableRef()
      ?.getCustomStoreData();

    const method = this.isCustomColumn
      ? "cancelCustomColumn"
      : "saveCustomColumn";
    this.btnLoading = true;

    const res = await comApi[method]({
      menuFlag: "uniqueMenuFlag", // 每个页面使用唯一的menuFlag
      preferenceJson: JSON.stringify(currentStoreData),
    }).catch(() => {
      this.btnLoading = false;
    });

    this.btnLoading = false;
    const text = this.isCustomColumn
      ? "表格列偏好设置已取消"
      : "表格列偏好设置已开启";

    if (res.code === "10000") {
      this.isCustomColumn = !this.isCustomColumn;
      this.$message.success(text);
      this.restorePromise = null; // 清除缓存，下次重新获取
    }
  },

  // 初始化表格列偏好设置
  async initTablePreference() {
    try {
      const res = await comApi.queryCustomColumn({
        menuFlag: "uniqueMenuFlag", // 每个页面使用唯一的menuFlag
      });
      if (res.code === "10000") {
        this.isCustomColumn = res.data.preferenceFlag === "Y";
        this.isPreferenceInitialized = true;
        if (this.isCustomColumn && res.data.preferenceJson) {
          this.storeData = JSON.parse(res.data.preferenceJson);
        }
      }
    } catch (error) {
      console.error("Failed to load table preferences:", error);
      this.isPreferenceInitialized = true; // 即使失败也标记为已初始化
    }
  },
}
```

## 4. API 接口

### 4.1 查询偏好设置

```javascript
// GET /ledger/order/queryPreference
queryCustomColumn(query) {
  return request({
    url: "/ledger/order/queryPreference",
    method: "get",
    params: query, // { menuFlag: "uniqueMenuFlag" }
  });
}
```

### 4.2 保存偏好设置

```javascript
// POST /ledger/order/savePreference
saveCustomColumn(query) {
  return request({
    url: "/ledger/order/savePreference",
    method: "post",
    data: query, // { menuFlag: "uniqueMenuFlag", preferenceJson: "..." }
  });
}
```

### 4.3 删除偏好设置

```javascript
// GET /ledger/order/removePreference
cancelCustomColumn(query) {
  return request({
    url: "/ledger/order/removePreference",
    method: "get",
    params: query, // { menuFlag: "uniqueMenuFlag" }
  });
}
```

## 5. 关键配置说明

### 5.1 menuFlag 命名规范

- 每个页面必须使用唯一的 menuFlag 标识
- 建议使用页面路径或功能模块名称
- 示例：`emailOriginalData`、`ledgerList`、`dashboardDetail`

### 5.2 表格 ID 命名规范

- 每个表格必须设置唯一的 id 属性
- 建议使用页面名称+Id 后缀
- 示例：`emailOriginalDataId`、`ledgerListId`、`dashboardDetailId`

### 5.3 Promise 缓存机制

- 使用 restorePromise 缓存初始化请求，避免重复调用
- 在取消偏好设置后清除缓存：`this.restorePromise = null`

## 6. 注意事项

1. **唯一性**: 确保每个页面的 menuFlag 和表格 ID 都是唯一的
2. **错误处理**: 在 API 调用失败时要有适当的错误处理
3. **加载状态**: 使用 btnLoading 控制按钮的加载状态
4. **初始化标记**: 使用 isPreferenceInitialized 避免重复初始化
5. **数据格式**: preferenceJson 存储的是 JSON 字符串格式的配置数据

## 7. 扩展功能

### 7.1 筛选条件偏好设置

除了表格列偏好，还可以实现筛选条件的偏好设置，使用不同的 menuFlag 区分：

- 表格列偏好：`menuFlag: "pageName"`
- 筛选条件偏好：`menuFlag: "pageNameFilter"`

### 7.2 多表格页面

对于包含多个表格的页面，每个表格都需要：

- 独立的 menuFlag
- 独立的表格 ID
- 独立的偏好设置状态管理

## 8. 公共功能提取分析

### 8.1 当前实现方式的问题

1. **代码重复**: 每个页面都需要重复编写相同的偏好设置逻辑
2. **维护困难**: 当 API 接口变更时，需要修改多个页面
3. **一致性问题**: 不同页面的实现可能存在细微差异

### 8.2 提取为公共功能的可行性分析

#### 8.2.1 Mixin 方式（推荐）

创建一个表格偏好设置的 mixin，包含所有通用逻辑：

```javascript
// @/mixins/tablePreferenceMixin.js
export default {
  data() {
    return {
      storeData: {},
      btnLoading: false,
      restorePromise: null,
      isCustomColumn: false,
      isPreferenceInitialized: false,
    };
  },
  methods: {
    async handleCustom(menuFlag) {
      // 通用的偏好设置处理逻辑
    },
    async initTablePreference(menuFlag) {
      // 通用的初始化逻辑
    },
    getTableCustomConfig(menuFlag, tableId) {
      // 返回customConfig配置
    },
  },
};
```

#### 8.2.2 修改 BuseCrud 组件源码（不推荐）

虽然技术上可行，但不推荐直接修改 node_modules 中的源码，原因：

1. **升级风险**: 组件库升级时会丢失修改
2. **维护困难**: 难以追踪和管理自定义修改
3. **团队协作**: 其他开发者可能不知道有自定义修改

### 8.3 推荐的公共功能实现方案

#### 8.3.1 创建 Mixin

```javascript
// src/mixins/tablePreferenceMixin.js
import * as comApi from "@/api/common.js";

export default {
  data() {
    return {
      // 偏好设置相关数据
      tablePreference: {
        storeData: {},
        btnLoading: false,
        restorePromise: null,
        isCustomColumn: false,
        isPreferenceInitialized: false,
      },
    };
  },
  methods: {
    // 获取表格自定义配置
    getTableCustomConfig(menuFlag, tableId) {
      const that = this;
      return {
        storage: true,
        checkMethod: ({ column }) => true,
        restoreStore: () => {
          if (!that.tablePreference.restorePromise) {
            that.tablePreference.restorePromise = (async () => {
              try {
                const res = await comApi.queryCustomColumn({ menuFlag });
                that.tablePreference.isCustomColumn =
                  res.data.preferenceFlag === "Y";
                that.tablePreference.isPreferenceInitialized = true;
                const preferenceData = that.tablePreference.isCustomColumn
                  ? JSON.parse(res.data.preferenceJson)
                  : {};
                that.tablePreference.storeData = preferenceData;
                return preferenceData;
              } catch (e) {
                that.tablePreference.isPreferenceInitialized = true;
                that.tablePreference.storeData = {};
                return {};
              }
            })();
          }
          return that.tablePreference.restorePromise;
        },
        updateStore({ id, type, storeData }) {
          return new Promise(async (resolve) => {
            that.tablePreference.storeData = storeData;
            if (that.tablePreference.isCustomColumn) {
              const res = await comApi.saveCustomColumn({
                preferenceJson: JSON.stringify(storeData),
                menuFlag,
              });
              if (res.code === "10000") {
                that.$message.success("保存成功");
              }
            }
            resolve();
          });
        },
      };
    },
    // 处理表格列偏好设置
    async handleTableCustom(menuFlag, crudRef = "crud") {
      const currentStoreData = this.$refs[crudRef]
        .getVxeTableRef()
        ?.getCustomStoreData();
      const method = this.tablePreference.isCustomColumn
        ? "cancelCustomColumn"
        : "saveCustomColumn";
      this.tablePreference.btnLoading = true;

      const res = await comApi[method]({
        menuFlag,
        preferenceJson: JSON.stringify(currentStoreData),
      }).catch(() => {
        this.tablePreference.btnLoading = false;
      });

      this.tablePreference.btnLoading = false;
      const text = this.tablePreference.isCustomColumn
        ? "表格列偏好设置已取消"
        : "表格列偏好设置已开启";
      if (res.code === "10000") {
        this.tablePreference.isCustomColumn = !this.tablePreference
          .isCustomColumn;
        this.$message.success(text);
        this.tablePreference.restorePromise = null;
      }
    },
  },
};
```

#### 8.3.2 使用方式

```javascript
// 在页面组件中使用
import tablePreferenceMixin from "@/mixins/tablePreferenceMixin.js";

export default {
  mixins: [tablePreferenceMixin],
  computed: {
    tableProps() {
      return {
        // 其他配置...
        customConfig: this.getTableCustomConfig(
          "dashboardDetail",
          "dashboardDetailId"
        ),
        id: "dashboardDetailId",
      };
    },
  },
  methods: {
    handleCustom() {
      return this.handleTableCustom("dashboardDetail");
    },
  },
};
```

### 8.4 实施建议

1. **第一步**: 创建 tablePreferenceMixin.js
2. **第二步**: 在现有页面中逐步替换为 mixin 方式
3. **第三步**: 新页面直接使用 mixin
4. **第四步**: 考虑将 mixin 集成到项目的基础组件中
